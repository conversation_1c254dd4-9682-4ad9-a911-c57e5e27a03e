'use client';

import React, { useState } from 'react';
import JobSpecificationsCard from '@/components/features/JobSpecificationsCard';
import PaperOptionsTable from '@/components/features/PaperOptionsTable';
import ProductionParametersCard from '@/components/features/ProductionParametersCard';
import ResultsSection from '@/components/features/ResultsSection';
import UnitConverterCard from '@/components/features/UnitConverterCard';
import CoverSpecificationsCard from '@/components/features/CoverSpecificationsCard';
import CoverProductionParametersCard from '@/components/features/CoverProductionParametersCard';
import EndpapersSpecificationsCard from '@/components/features/EndpapersSpecificationsCard';
import EndpapersProductionParametersCard from '@/components/features/EndpapersProductionParametersCard';
import Sidebar from '@/components/layout/Sidebar';
import SummaryPanel from '@/components/features/SummaryPanel';

import { useSidebar } from '@/contexts/SidebarContext';
// import { useLanguage } from '@/contexts/LanguageContext'; // Removed unused import statement

import { CalculationResult } from '@/utils/calculationEngine';

type ComponentType = 'innerText' | 'cover' | 'endpapers';



export default function HomePage() {
  const [activeTab, setActiveTab] = useState<ComponentType>('innerText');
  const [calculationResults, setCalculationResults] = useState<CalculationResult[]>([]);
  const { isCollapsed } = useSidebar();

  const handleTabClick = (tab: ComponentType) => {
    setActiveTab(tab);
    setCalculationResults([]); // Clear results when switching tabs
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 text-neutral-800 dark:from-neutral-900 dark:to-neutral-950 dark:text-neutral-100 transition-all duration-500'>
      <Sidebar activeTab={activeTab} onTabChange={handleTabClick} />
      <SummaryPanel />

      <main className={`main-content-with-sidebar ${isCollapsed ? 'sidebar-collapsed' : ''} mx-auto max-w-7xl py-8 premium-section`}>
        {/* Inner Text Tab */}
        {activeTab === 'innerText' && (
          <div className='animate-fadeIn'>
            {/* Clean Top Row - Three Cards Side by Side */}
            <div className='top-row-grid animate-slideUp'>
              <JobSpecificationsCard />
              <ProductionParametersCard />
              <UnitConverterCard />
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="innerText" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}

        {/* Cover Tab */}
        {activeTab === 'cover' && (
          <div className='animate-fadeIn'>
            {/* Clean Top Row - Three Cards Side by Side */}
            <div className='top-row-grid animate-slideUp'>
              <CoverSpecificationsCard />
              <CoverProductionParametersCard />
              <UnitConverterCard />
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="cover" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}

        {/* Endpapers Tab */}
        {activeTab === 'endpapers' && (
          <div className='animate-fadeIn'>
            {/* Clean Top Row - Three Cards Side by Side */}
            <div className='top-row-grid animate-slideUp'>
              <EndpapersSpecificationsCard />
              <EndpapersProductionParametersCard />
              <UnitConverterCard />
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="endpapers" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
