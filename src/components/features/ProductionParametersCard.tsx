'use client';

import React from 'react';
import { useInnerTextStore, InnerTextProdParams } from '@/stores/innerTextStore';
import CardWrapper from '@/components/ui/CardWrapper';

const ProductionParametersCard = () => {
  const { prodParams, setProdParam, toggleDoubleLip, toggleAlignmentMode } = useInnerTextStore();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof InnerTextProdParams
  ) => {
    setProdParam(field, e.target.value);
  };

  return (
    <CardWrapper>
      <div className='card-header'>
        <h2 className='text-xl font-semibold' data-translate-key='prodParamsTitle'>
          Production Parameters
        </h2>
      </div>

      <div className="card-content">
        <div className="grid grid-cols-2 gap-4 mb-4">
        {/* Bleed */}
        <div>
          <label htmlFor="bleedMm" className="form-label" data-translate-key="bleedLabel">
            Bleed (mm)
          </label>
          <input
            type="number"
            id="bleedMm"
            className="form-input"
            value={prodParams.bleedMm}
            onChange={(e) => handleInputChange(e, 'bleedMm')}
            step="0.1"
            placeholder="e.g., 3"
          />
        </div>

        {/* Gripper */}
        <div>
          <label htmlFor="gripperMm" className="form-label" data-translate-key="gripperLabel">
            Gripper (mm)
          </label>
          <input
            type="number"
            id="gripperMm"
            className="form-input"
            value={prodParams.gripperMm}
            onChange={(e) => handleInputChange(e, 'gripperMm')}
            step="0.1"
            placeholder="e.g., 12"
          />
        </div>

        {/* Color Bar */}
        <div>
          <label htmlFor="colorBarMm" className="form-label" data-translate-key="colorBarLabel">
            Color Bar (mm)
          </label>
          <input
            type="number"
            id="colorBarMm"
            className="form-input"
            value={prodParams.colorBarMm}
            onChange={(e) => handleInputChange(e, 'colorBarMm')}
            step="0.1"
            placeholder="e.g., 6"
          />
        </div>

        {/* Side Lip */}
        <div>
          <label htmlFor="lipMm" className="form-label" data-translate-key="sideLipLabel">
            Side Lip (mm)
          </label>
          <div className="flex items-center gap-2">
            <input
              type="number"
              id="lipMm"
              className="form-input flex-1"
              value={prodParams.lipMm}
              onChange={(e) => handleInputChange(e, 'lipMm')}
              step="0.1"
              placeholder="e.g., 5"
            />
            <div
              id="double-lip-switcher_innerText"
              className={`double-lip-switch ${prodParams.isDoubleLipActive ? 'active' : ''}`}
              title="Double Side Lip"
              onClick={toggleDoubleLip}
              role="switch"
              aria-checked={prodParams.isDoubleLipActive}
            >
              <div className="double-lip-switch-toggle"></div>
            </div>
            <span className="double-lip-text"></span>
          </div>
        </div>

        {/* Grain Alignment - spans 2 columns */}
        <div className="col-span-2">
          <label className="form-label" data-translate-key="grainAlignmentLabel">
            Grain Alignment
          </label>
          <div className="flex items-center">
            <div
              id="alignment-mode-switcher_innerText"
              className={`alignment-switch ${prodParams.alignmentMode === 'Misaligned' ? 'misaligned' : ''}`}
              title="Switch Grain Alignment"
              onClick={toggleAlignmentMode}
              role="switch"
              aria-checked={prodParams.alignmentMode === 'Aligned'}
            >
              <div className="alignment-switch-toggle"></div>
            </div>
            <span className="ml-2 text-sm">
              <span className={`alignment-mode-text ${prodParams.alignmentMode === 'Misaligned' ? 'misaligned-text' : 'aligned-text'}`}>
                {prodParams.alignmentMode === 'Misaligned' ? 'Misaligned' : 'Aligned'}
              </span>
            </span>
          </div>
        </div>

        <p className="text-xs text-neutral-500 mt-auto dark-mode-text" data-translate-key="prodParamsNote">
          * Gutter not used for sheet area. Margins applied to 720H x 1020W press.
        </p>
      </div>
    </CardWrapper>
  );
};

export default ProductionParametersCard;
