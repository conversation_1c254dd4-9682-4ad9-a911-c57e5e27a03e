'use client';

import React, { useEffect } from 'react';
import { useCoverStore, CoverJobSpecs } from '@/stores/coverStore';
import { useInnerTextStore } from '@/stores/innerTextStore'; // To potentially auto-fill from Inner Text
import { mmToIn, inToMm } from '@/utils/conversions'; // Assuming these are in conversions.ts
import CardWrapper from '@/components/ui/CardWrapper';

const CoverSpecificationsCard = () => {
  const { jobSpecs, setJobSpec } = useCoverStore();
  const { jobSpecs: innerTextJobSpecs } = useInnerTextStore(); // Get Inner Text job specs

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    field: keyof CoverJobSpecs
  ) => {
    let value = e.target.value;
    setJobSpec(field, value);

    // Synchronize mm/inch fields
    if (field === 'trimHeightMm') {
      setJobSpec('trimHeightIn', mmToIn(value));
    } else if (field === 'trimHeightIn') {
      setJobSpec('trimHeightMm', inToMm(value));
    } else if (field === 'trimWidthMm') {
      setJobSpec('trimWidthIn', mmToIn(value));
      // Auto-calculate flap width if cover type is dust jacket and flap width is empty
      if (jobSpecs.coverType === 'dustJacket' && !jobSpecs.flapWidthMm) {
         const bookWidth = parseFloat(value);
         if (!isNaN(bookWidth) && bookWidth > 0) {
             setJobSpec('flapWidthMm', (bookWidth / 2).toFixed(1));
         }
      }
    } else if (field === 'trimWidthIn') {
      setJobSpec('trimWidthMm', inToMm(value));
       // Auto-calculate flap width if cover type is dust jacket and flap width is empty
      if (jobSpecs.coverType === 'dustJacket' && !jobSpecs.flapWidthMm) {
         const bookWidth = parseFloat(inToMm(value)); // Convert inches to mm first
         if (!isNaN(bookWidth) && bookWidth > 0) {
             setJobSpec('flapWidthMm', (bookWidth / 2).toFixed(1));
         }
      }
    } else if (field === 'spineThicknessMm') {
      setJobSpec('spineThicknessIn', mmToIn(value));
    } else if (field === 'spineThicknessIn') {
      setJobSpec('spineThicknessMm', inToMm(value));
    }
  };

  // Effect to auto-fill from Inner Text if fields are empty
  useEffect(() => {
    // Only auto-fill if Inner Text has valid dimensions and quantity
    const innerH = parseFloat(innerTextJobSpecs.trimHeightMm);
    const innerW = parseFloat(innerTextJobSpecs.trimWidthMm);
    const innerQty = parseInt(innerTextJobSpecs.quantity, 10);

    if (!isNaN(innerH) && innerH > 0 && !isNaN(innerW) && innerW > 0 && !isNaN(innerQty) && innerQty > 0) {
      if (!jobSpecs.trimHeightMm) {
        setJobSpec('trimHeightMm', innerTextJobSpecs.trimHeightMm);
        setJobSpec('trimHeightIn', mmToIn(innerTextJobSpecs.trimHeightMm));
      }
      if (!jobSpecs.trimWidthMm) {
        setJobSpec('trimWidthMm', innerTextJobSpecs.trimWidthMm);
        setJobSpec('trimWidthIn', mmToIn(innerTextJobSpecs.trimWidthMm));
      }
      if (!jobSpecs.quantity) {
        setJobSpec('quantity', innerTextJobSpecs.quantity);
      }
      // TODO: Auto-fill spine thickness from Inner Text calculation result if available
    }
  }, [innerTextJobSpecs, jobSpecs, setJobSpec]);

  // Effect to toggle visibility and auto-calculate flap width based on cover type
  useEffect(() => {
    const turnInSection = document.getElementById('turn-in-allowance-section_cover');
    const flapWidthSection = document.getElementById('flap-width-section_cover');
    const coverTrimW = jobSpecs.trimWidthMm; // Use value from state

    if (turnInSection) {
      if (jobSpecs.coverType === 'hardcover') {
        turnInSection.classList.remove('hidden');
      } else {
        turnInSection.classList.add('hidden');
      }
    }

    if (flapWidthSection) {
      if (jobSpecs.coverType === 'dustJacket') {
        flapWidthSection.classList.remove('hidden');
        // Auto-calculate flap width if not already set
        if (!jobSpecs.flapWidthMm && coverTrimW) {
            const bookWidth = parseFloat(coverTrimW);
            if (!isNaN(bookWidth) && bookWidth > 0) {
                setJobSpec('flapWidthMm', (bookWidth / 2).toFixed(1));
            }
        }
      } else {
        flapWidthSection.classList.add('hidden');
      }
    }
  }, [jobSpecs.coverType, jobSpecs.trimWidthMm, jobSpecs.flapWidthMm, setJobSpec]);


  return (
    <CardWrapper>
      <div className='card-header'>
        <h2 className='text-xl font-semibold' data-translate-key='coverSpecsTitle'>
          Cover Specifications
        </h2>
      </div>
      <div className='card-content space-y-4'>
        <div>
          <div className='mb-1 grid grid-cols-2 gap-x-4'>
            <label htmlFor='trimHeightMm' className='form-label' data-translate-key='coverHeightLabel'>
              Cover Height (mm)
            </label>
            <label htmlFor='trimHeightIn' className='form-label' data-translate-key='coverHeightLabelIn'>
              Cover Height (in)
            </label>
          </div>
          <div className='flex items-center gap-3'>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimHeightMm'
                className='form-input pr-8'
                value={jobSpecs.trimHeightMm}
                onChange={(e) => handleInputChange(e, 'trimHeightMm')}
                step='0.1'
                placeholder='e.g., 225'
                data-placeholder-translate-key='placeholderCoverHeight'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>mm</span>
            </div>
            <div className='conversion-separator'>
              ⟷
            </div>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimHeightIn'
                className='form-input pr-8'
                value={jobSpecs.trimHeightIn}
                onChange={(e) => handleInputChange(e, 'trimHeightIn')}
                step='0.01'
                placeholder='e.g., 8.86'
                data-placeholder-translate-key='placeholderCoverHeightIn'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>in</span>
            </div>
          </div>
        </div>
        <div>
          <div className='mb-1 grid grid-cols-2 gap-x-4'>
            <label htmlFor='trimWidthMm' className='form-label' data-translate-key='coverWidthLabel'>
              Cover Width (mm)
            </label>
            <label htmlFor='trimWidthIn' className='form-label' data-translate-key='coverWidthLabelIn'>
              Cover Width (in)
            </label>
          </div>
          <div className='flex items-center gap-3'>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimWidthMm'
                className='form-input pr-8'
                value={jobSpecs.trimWidthMm}
                onChange={(e) => handleInputChange(e, 'trimWidthMm')}
                step='0.1'
                placeholder='e.g., 150'
                data-placeholder-translate-key='placeholderCoverWidth'
                data-title-translate-key='coverDimNote'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>mm</span>
            </div>
            <div className='conversion-separator'>
              ⟷
            </div>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimWidthIn'
                className='form-input pr-8'
                value={jobSpecs.trimWidthIn}
                onChange={(e) => handleInputChange(e, 'trimWidthIn')}
                step='0.01'
                placeholder='e.g., 5.91'
                data-placeholder-translate-key='placeholderCoverWidthIn'
                data-title-translate-key='coverDimNote'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>in</span>
            </div>
          </div>
        </div>
        <div>
          <div className='mb-1 grid grid-cols-2 gap-x-4'>
            <label htmlFor='spineThicknessMm' className='form-label' data-translate-key='spineThicknessLabel'>
              Spine Thickness (mm)
            </label>
            <label htmlFor='spineThicknessIn' className='form-label' data-translate-key='spineThicknessLabelIn'>
              Spine Thickness (in)
            </label>
          </div>
          <div className='flex items-center gap-3'>
            <div className='relative flex-1'>
              <input
                type='number'
                id='spineThicknessMm'
                className='form-input pr-8'
                value={jobSpecs.spineThicknessMm}
                onChange={(e) => handleInputChange(e, 'spineThicknessMm')}
                step='0.1'
                placeholder='e.g., 15 or auto'
                data-placeholder-translate-key='placeholderSpineThickness'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>mm</span>
            </div>
            <div className='conversion-separator'>
              ⟷
            </div>
            <div className='relative flex-1'>
              <input
                type='number'
                id='spineThicknessIn'
                className='form-input pr-8'
                value={jobSpecs.spineThicknessIn}
                onChange={(e) => handleInputChange(e, 'spineThicknessIn')}
                step='0.01'
                placeholder='e.g., 0.59 or auto'
                data-placeholder-translate-key='placeholderSpineThicknessIn'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>in</span>
            </div>
          </div>
        </div>
        {/* Responsive grid for input groups */}
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
          <div className='md:col-span-1'> {/* Cover Type */}
            <label htmlFor='coverType' className='form-label' data-translate-key='coverTypeLabel'>
              Cover Type
            </label>
            <select
              id='coverType'
              className='form-select'
              value={jobSpecs.coverType}
              onChange={(e) => handleInputChange(e, 'coverType')}
            >
              <option value='paperback' data-translate-key='coverTypePaperback'>
                Paperback
              </option>
              <option value='hardcover' data-translate-key='coverTypeHardcover'>
                Hardcover (Case)
              </option>
              <option value='dustJacket' data-translate-key='coverTypeDustJacket'>
                Dust Jacket
              </option>
            </select>
          </div>
          <div className='md:col-span-1'> {/* Quantity */}
            <label htmlFor='quantity' className='form-label' data-translate-key='quantityLabel'>
              Quantity
            </label>
            <input
              type='number'
              id='quantity'
              className='form-input'
              value={jobSpecs.quantity}
              onChange={(e) => handleInputChange(e, 'quantity')}
              placeholder='e.g., 1000'
              data-placeholder-translate-key='placeholderQuantity'
            />
          </div>
          <div className='md:col-span-1'> {/* Spoilage */}
            <label htmlFor='spoilagePercent' className='form-label' data-translate-key='spoilageLabel'>
              Spoilage (%)
            </label>
            <input
              type='number'
              id='spoilagePercent'
              className='form-input'
              value={jobSpecs.spoilagePercent}
              onChange={(e) => handleInputChange(e, 'spoilagePercent')}
              step='0.1'
              placeholder='e.g., 7'
              data-placeholder-translate-key='placeholderSpoilage'
            />
          </div>
          {/* Flap Width Section - Visibility controlled by useEffect */}
          <div id='flap-width-section_cover' className='hidden md:col-span-1'> {/* Will show in 3-col layout */}
            <label htmlFor='flapWidthMm' className='form-label' data-translate-key='flapWidthLabel'>
              Flap Width (mm)
            </label>
            <input
              type='number'
              id='flapWidthMm'
              className='form-input'
              value={jobSpecs.flapWidthMm}
              onChange={(e) => handleInputChange(e, 'flapWidthMm')}
              step='0.1'
              placeholder='e.g., half book width'
              data-placeholder-translate-key='placeholderFlapWidth'
              data-title-translate-key='flapWidthNote'
            />
          </div>
        </div>
      </div>
    </CardWrapper>
  );
};

export default CoverSpecificationsCard;
