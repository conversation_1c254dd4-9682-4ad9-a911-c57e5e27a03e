import React from 'react';

interface CardWrapperProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * CardWrapper component that provides consistent styling for card components
 * Uses the clean card-container styling for premium glass morphism effect
 */
const CardWrapper: React.FC<CardWrapperProps> = ({ children, className = '' }) => {
  return (
    <div className={`card-container ${className}`}>
      {children}
    </div>
  );
};

export default CardWrapper;
